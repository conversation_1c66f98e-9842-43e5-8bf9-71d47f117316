/**
 * Mobile Exercise Page Set Model
 *
 * TypeScript interface mirroring C# WorkoutLogSerieModelRef from mobile app
 * Used for exercise page set display and state management
 */

import type { MultiUnityWeight, WorkoutLogSerieModel } from './workout'

/**
 * Extended workout log series model with mobile-specific UI states
 * Extends WorkoutLogSerieModel with additional properties for mobile exercise page
 */
export interface WorkoutLogSerieModelRef extends WorkoutLogSerieModel {
  // Exercise identification
  ExerciseName: string
  EquipmentId?: number

  // Set state management
  IsNext: boolean
  IsFinished: boolean
  IsActive: boolean
  IsEditing: boolean

  // Set positioning and metadata
  SetNo: string
  SetTitle: string
  LastTimeSet: string
  IsLastSet: boolean
  IsFirstSide: boolean
  IsHeaderCell: boolean
  IsFirstSetFinished: boolean
  IsFirstWorkSet: boolean
  IsLastWarmupSet: boolean

  // Exercise completion tracking
  IsExerciseFinished: boolean
  IsJustSetup: boolean
  ShouldUpdateIncrement: boolean

  // Set type identification
  IsBackOffSet: boolean
  IsNextBackOffSet: boolean
  IsDropSet: boolean
  IsNormalset: boolean
  IsMaxChallenge: boolean
  IsAssisted: boolean

  // Exercise characteristics
  IsFlexibility: boolean
  IsTimeBased: boolean
  IsUnilateral: boolean
  IsBodyweight: boolean
  IsTimerOff: boolean
  IsSizeChanged: boolean

  // Timer and progression
  ShowWorkTimer: boolean
  NbPause: number
  OneRMProgress: number

  // Weight and progression data
  PreviousReps: number
  PreviousWeight: MultiUnityWeight
  Speed: number
  Increments: MultiUnityWeight
  Min: MultiUnityWeight
  Max: MultiUnityWeight

  // Header and media
  HeaderImage: string
  HeaderTitle: string
  VideoUrl: string

  // UI helpers
  ShowPlusTooltip: boolean
  ShowSuperSet3: boolean
  ShowSuperSet2: boolean

  // Computed properties
  readonly BackColor: string
  readonly WeightSingal: string // Note: Typo from C# model
  readonly WeightDouble: string
}

/**
 * Exercise work sets collection model
 * Represents a complete exercise with all its sets
 */
export interface ExerciseWorkSetsModel {
  // Exercise identification
  Id: number
  Label: string
  CountNo: string

  // Exercise characteristics
  IsBodyweight: boolean
  IsSystemExercise: boolean
  IsFlexibility: boolean
  IsTimeBased: boolean
  IsUnilateral: boolean

  // Exercise state
  IsFinished: boolean
  IsNextExercise: boolean
  IsSelected: boolean
  IsRecoLoaded: boolean

  // Media and display
  VideoUrl: string
  HeaderImage: string
  BodyPartId?: number

  // Sets collection
  Sets: WorkoutLogSerieModelRef[]

  // UI state flags
  IsAddExercise?: boolean
  IsFinishWorkoutExe?: boolean

  // Methods
  Clear(): void
  Count: number
  Add(set: WorkoutLogSerieModelRef): void
}

/**
 * Mass unit setting for weight display
 */
export type MassUnit = 'kg' | 'lb'

/**
 * Settings for exercise display
 */
export interface ExerciseDisplaySettings {
  massUnit: MassUnit
  showPlusTooltip: boolean
  showSuperSet2: boolean
  showSuperSet3: boolean
  timerEnabled: boolean
}

/**
 * Exercise page state management
 */
export interface ExercisePageState {
  currentExercise: ExerciseWorkSetsModel | null
  exercises: ExerciseWorkSetsModel[]
  settings: ExerciseDisplaySettings
  isLoading: boolean
  error: string | null
}
