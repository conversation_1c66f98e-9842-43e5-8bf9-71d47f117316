import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { WorkoutLogSerieModel } from '@/types'

describe('ExerciseSetsGrid', () => {
  const mockExercise = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
  }

  const mockSets: WorkoutLogSerieModel[] = [
    {
      Id: 1,
      SetNo: '1',
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      IsFinished: true,
      IsNext: false,
      IsWarmups: false,
    },
    {
      Id: 2,
      SetNo: '2',
      Reps: 8,
      Weight: { Lb: 145, Kg: 65.8 },
      IsFinished: false,
      IsNext: true,
      IsWarmups: false,
    },
    {
      Id: 3,
      SetNo: '3',
      Reps: 6,
      Weight: { Lb: 155, Kg: 70.3 },
      IsFinished: false,
      IsNext: false,
      IsWarmups: false,
    },
  ]

  describe('Grid Display', () => {
    it('should render header row with SET | REPS | * | LBS columns', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      expect(screen.getByText('SET')).toBeInTheDocument()
      expect(screen.getByText('REPS')).toBeInTheDocument()
      expect(screen.getByText('LBS')).toBeInTheDocument()
    })

    it('should render all sets in grid layout', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      // Check all set numbers are displayed
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()

      // Check reps are displayed as inputs
      expect(screen.getByDisplayValue('10')).toBeInTheDocument()
      expect(screen.getByDisplayValue('8')).toBeInTheDocument()
      expect(screen.getByDisplayValue('6')).toBeInTheDocument()

      // Check weights are displayed
      expect(screen.getByDisplayValue('135')).toBeInTheDocument()
      expect(screen.getByDisplayValue('145')).toBeInTheDocument()
      expect(screen.getByDisplayValue('155')).toBeInTheDocument()
    })

    it('should show exercise name at the top', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })
  })

  describe('Set Interactions', () => {
    it('should handle reps change for a set', () => {
      const onSetUpdate = vi.fn()
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={onSetUpdate}
        />
      )

      const secondSetReps = screen.getByDisplayValue('8')
      fireEvent.change(secondSetReps, { target: { value: '10' } })

      expect(onSetUpdate).toHaveBeenCalledWith(2, { reps: 10 })
    })

    it('should handle weight change for a set', () => {
      const onSetUpdate = vi.fn()
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={onSetUpdate}
        />
      )

      const secondSetWeight = screen.getByDisplayValue('145')
      fireEvent.change(secondSetWeight, { target: { value: '150' } })

      expect(onSetUpdate).toHaveBeenCalledWith(2, { weight: 150 })
    })

    it('should disable editing for finished sets', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      const firstSetReps = screen.getByDisplayValue('10')
      const firstSetWeight = screen.getByDisplayValue('135')

      expect(firstSetReps).toBeDisabled()
      expect(firstSetWeight).toBeDisabled()
    })

    it('should disable weight input for bodyweight exercises', () => {
      const bodyweightExercise = { ...mockExercise, IsBodyweight: true }
      const bodyweightSets = mockSets.map((set) => ({
        ...set,
        Weight: { Lb: 0, Kg: 0 },
      }))

      render(
        <ExerciseSetsGrid
          exercise={bodyweightExercise}
          sets={bodyweightSets}
          onSetUpdate={vi.fn()}
        />
      )

      const weightInputs = screen.getAllByDisplayValue('0')
      weightInputs.forEach((input) => {
        if (input.getAttribute('aria-label')?.includes('Weight')) {
          expect(input).toBeDisabled()
        }
      })
    })
  })

  describe('Visual States', () => {
    it('should show check icon for finished sets', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      const checkIcons = screen.getAllByTestId('check-icon')
      expect(checkIcons).toHaveLength(1) // Only first set is finished
    })

    it('should show delete icon for unfinished sets', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      const deleteIcons = screen.getAllByTestId('delete-icon')
      expect(deleteIcons).toHaveLength(2) // Second and third sets are unfinished
    })

    it('should highlight the current/next set', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      // The second set has IsNext: true
      // Find the input with value 8 (second set reps) and check its container
      const secondSetRepsInput = screen.getByDisplayValue('8')
      // Go up to the SetCell's container div that has the ring classes
      const setContainer = secondSetRepsInput.closest('[class*="grid-cols"]')
        ?.parentElement?.parentElement
      expect(setContainer).toHaveClass('ring-2')
    })
  })

  describe('Last Set Features', () => {
    it('should show "All sets done" message when all sets are finished', () => {
      const allFinishedSets = mockSets.map((set) => ({
        ...set,
        IsFinished: true,
      }))

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={allFinishedSets}
          onSetUpdate={vi.fn()}
        />
      )

      expect(screen.getByText('All sets done—congrats!')).toBeInTheDocument()
    })

    it('should show "Finish exercise" button when all sets are done', () => {
      const allFinishedSets = mockSets.map((set) => ({
        ...set,
        IsFinished: true,
      }))
      const onFinishExercise = vi.fn()

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={allFinishedSets}
          onSetUpdate={vi.fn()}
          onFinishExercise={onFinishExercise}
        />
      )

      const finishButton = screen.getByText('Finish exercise')
      expect(finishButton).toBeInTheDocument()

      fireEvent.click(finishButton)
      expect(onFinishExercise).toHaveBeenCalled()
    })

    it('should show "Add set" button on last set', () => {
      const onAddSet = vi.fn()
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
          onAddSet={onAddSet}
        />
      )

      const addSetButton = screen.getByText('Add set')
      expect(addSetButton).toBeInTheDocument()

      fireEvent.click(addSetButton)
      expect(onAddSet).toHaveBeenCalled()
    })
  })

  describe('Empty State', () => {
    it('should show empty state when no sets', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={[]}
          onSetUpdate={vi.fn()}
          onAddSet={vi.fn()}
        />
      )

      expect(screen.getByText('No sets for this exercise')).toBeInTheDocument()
      expect(screen.getByText('Add set')).toBeInTheDocument()
    })
  })

  describe('Weight Unit Display', () => {
    it('should display weight in kilograms when unit is kg', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
          unit="kg"
        />
      )

      expect(screen.getByText('KG')).toBeInTheDocument()
      expect(screen.getByDisplayValue('61.2')).toBeInTheDocument()
      expect(screen.getByDisplayValue('65.8')).toBeInTheDocument()
      expect(screen.getByDisplayValue('70.3')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for all inputs', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={mockSets}
          onSetUpdate={vi.fn()}
        />
      )

      expect(screen.getByLabelText('Reps for set 1')).toBeInTheDocument()
      expect(screen.getByLabelText('Weight for set 1')).toBeInTheDocument()
      expect(screen.getByLabelText('Reps for set 2')).toBeInTheDocument()
      expect(screen.getByLabelText('Weight for set 2')).toBeInTheDocument()
    })
  })
})
