import { renderHook } from '@testing-library/react'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('next/navigation')
vi.mock('@/utils/debugLog', () => ({
  debugLog: vi.fn(() => {}),
}))

describe('useExercisePageInitialization - Infinite Loop Prevention', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockWorkout = {
    todaysWorkout: [
      {
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [
              {
                Id: 123,
                Label: 'Test Exercise',
              },
            ],
          },
        ],
      },
    ],
    isLoadingWorkout: false,
    startWorkout: vi.fn(),
    exercises: [
      {
        Id: 123,
        Label: 'Test Exercise',
        sets: [],
      },
    ],
    workoutSession: null,
    loadRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
  }

  const mockStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn(() => null),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useWorkout as any).mockReturnValue(mockWorkout)
    ;(useWorkoutStore as any).mockReturnValue(mockStore)
  })

  it('should not cause infinite re-renders when getCachedExerciseRecommendation changes', () => {
    let renderCount = 0

    // Track how many times getCachedExerciseRecommendation is created
    const getCachedExerciseRecommendationMock = vi.fn(() => null)

    // Simulate the function being recreated on each render
    ;(useWorkoutStore as any).mockImplementation(() => {
      renderCount++
      return {
        ...mockStore,
        // This simulates the real behavior where the function is recreated
        getCachedExerciseRecommendation: getCachedExerciseRecommendationMock,
      }
    })

    const { rerender } = renderHook(() => useExercisePageInitialization(123))

    // Force a few re-renders
    rerender()
    rerender()
    rerender()

    // Should have rendered a reasonable number of times, not infinitely
    // Without the fix, this would be much higher due to the infinite loop
    expect(renderCount).toBeLessThan(10)

    // The function should only be called once now that it's removed from dependencies
    expect(getCachedExerciseRecommendationMock).toHaveBeenCalledTimes(1)
  })

  it('should still load recommendations when cache is empty', () => {
    renderHook(() => useExercisePageInitialization(123))

    // Verify the hook still works correctly
    expect(mockStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
    expect(mockWorkout.loadRecommendation).toHaveBeenCalledWith(
      123,
      'Test Exercise'
    )
    expect(mockWorkout.updateExerciseWorkSets).toHaveBeenCalledWith(123, [])
  })
})
