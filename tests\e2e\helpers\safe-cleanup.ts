import { Page } from '@playwright/test'

/**
 * Safely close a Playwright page with defensive checks
 * Prevents "Target page, context or browser has been closed" errors
 */
export async function safePageClose(page: Page): Promise<void> {
  try {
    // Check if page is already closed
    if (!page || page.isClosed()) {
      return
    }

    // Attempt to close the page
    await page.close()
  } catch (error) {
    // Ignore errors if page is already closed
    if (
      error instanceof Error &&
      (error.message.includes(
        'Target page, context or browser has been closed'
      ) ||
        error.message.includes('Target closed') ||
        error.message.includes('Connection closed'))
    ) {
      // Expected error when page is already closed, ignore
      return
    }
    // Re-throw unexpected errors
    throw error
  }
}

/**
 * Ensure all browser contexts are properly cleaned up
 */
export async function ensureCleanBrowserState(): Promise<void> {
  // Add a small delay to allow any pending operations to complete
  await new Promise((resolve) => setTimeout(resolve, 100))
}
