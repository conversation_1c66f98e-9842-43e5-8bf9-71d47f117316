import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { SetListMobile } from '../SetListMobile'
import type {
  WorkoutLogSerieModelRef,
  ExerciseWorkSetsModel,
} from '@/types/api/WorkoutLogSerieModelRef'

describe('SetListMobile', () => {
  let mockExercise: ExerciseWorkSetsModel
  let mockSet1: WorkoutLogSerieModelRef
  let mockSet2: WorkoutLogSerieModelRef
  let mockSet3: WorkoutLogSerieModelRef

  beforeEach(() => {
    mockSet1 = {
      Id: 1,
      ExerciseId: 217,
      SerieOrder: 1,
      Reps: 10,
      Weight: { Kg: 20, Lb: 45 },
      IsWarmups: true,
      RIR: 0,
      IsAssisted: false,
      ExerciseName: 'Barbell bench press',
      IsNext: false,
      IsFinished: false,
      IsActive: false,
      IsEditing: false,
      SetNo: '1',
      SetTitle: 'Warmup 1',
      IsLastSet: false,
      IsFirstSide: true,
      IsHeaderCell: false,
      IsFirstSetFinished: false,
      IsFirstWorkSet: false,
      IsExerciseFinished: false,
      IsJustSetup: false,
      ShouldUpdateIncrement: false,
      IsBackOffSet: false,
      IsNextBackOffSet: false,
      IsDropSet: false,
      IsNormalset: false,
      IsMaxChallenge: false,
      IsFlexibility: false,
      IsTimeBased: false,
      IsUnilateral: false,
      IsBodyweight: false,
      IsTimerOff: false,
      IsSizeChanged: false,
      ShowWorkTimer: false,
      LastTimeSet: '',
      PreviousReps: 0,
      PreviousWeight: { Kg: 0, Lb: 0 },
      Speed: 1.0,
      Increments: { Kg: 2.5, Lb: 5 },
      Min: { Kg: 20, Lb: 45 },
      Max: { Kg: 200, Lb: 440 },
      HeaderImage: '',
      HeaderTitle: '',
      VideoUrl: '',
      ShowPlusTooltip: false,
      ShowSuperSet3: false,
      ShowSuperSet2: false,
      get BackColor() {
        return 'transparent'
      },
      get WeightSingal() {
        return '20.00'
      },
      get WeightDouble() {
        return '20.00'
      },
    }

    mockSet2 = {
      ...mockSet1,
      Id: 2,
      SerieOrder: 2,
      SetNo: '1',
      SetTitle: 'Set 1',
      Reps: 8,
      Weight: { Kg: 60, Lb: 132.28 },
      IsWarmups: false,
      IsFirstWorkSet: true,
      IsNext: true,
      IsNormalset: true,
      ShowWorkTimer: true,
      get BackColor() {
        return '#4D0C2432'
      },
      get WeightSingal() {
        return '60.00'
      },
      get WeightDouble() {
        return '60.00'
      },
    }

    mockSet3 = {
      ...mockSet1,
      Id: 3,
      SerieOrder: 3,
      SetNo: '2',
      SetTitle: 'Set 2',
      Reps: 8,
      Weight: { Kg: 60, Lb: 132.28 },
      IsWarmups: false,
      IsLastSet: true,
      IsNormalset: true,
      ShowWorkTimer: true,
      get WeightSingal() {
        return '60.00'
      },
      get WeightDouble() {
        return '60.00'
      },
    }

    mockExercise = {
      Id: 217,
      Label: 'Barbell bench press',
      CountNo: '1',
      IsBodyweight: false,
      IsSystemExercise: true,
      IsFlexibility: false,
      IsTimeBased: false,
      IsUnilateral: false,
      IsFinished: false,
      IsNextExercise: true,
      IsSelected: false,
      IsRecoLoaded: true,
      VideoUrl: '',
      HeaderImage: '',
      BodyPartId: 1,
      Sets: [mockSet1, mockSet2, mockSet3],
      Clear() {
        this.Sets = []
      },
      get Count() {
        return this.Sets.length
      },
      Add(set: WorkoutLogSerieModelRef) {
        this.Sets.push(set)
      },
    }
  })

  it('should render all sets', () => {
    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    expect(screen.getByText('Warmup 1')).toBeInTheDocument()
    expect(screen.getByText('Set 1')).toBeInTheDocument()
    expect(screen.getByText('Set 2')).toBeInTheDocument()
  })

  it('should display weight in correct unit', () => {
    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    expect(screen.getByText('20 kg × 10')).toBeInTheDocument()
    expect(screen.getAllByText('60 kg × 8')).toHaveLength(2)
  })

  it('should display weight in pounds when unit is lb', () => {
    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="lb" />
    )

    expect(screen.getByText('45 lb × 10')).toBeInTheDocument()
    expect(screen.getAllByText('132.28 lb × 8')).toHaveLength(2)
  })

  it('should highlight next set', () => {
    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    const nextSet = screen.getByTestId('set-1')
    expect(nextSet).toHaveClass('bg-primary/10')
  })

  it('should highlight finished sets', () => {
    mockSet1.IsFinished = true

    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    const finishedSet = screen.getByTestId('set-0')
    expect(finishedSet).toHaveClass('bg-primary/10')
  })

  it('should call onSetTap when set is tapped', () => {
    const onSetTap = vi.fn()

    render(
      <SetListMobile
        exercise={mockExercise}
        onSetTap={onSetTap}
        massUnit="kg"
      />
    )

    fireEvent.click(screen.getByTestId('set-1'))
    expect(onSetTap).toHaveBeenCalledWith(mockSet2, 1)
  })

  it('should show warmup indicator', () => {
    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    const warmupSet = screen.getByTestId('set-0')
    expect(warmupSet.querySelector('.text-orange-600')).toBeInTheDocument()
  })

  it('should show timer icon for sets with timer', () => {
    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    const timerSets = screen.getAllByTestId(/set-[1-2]/)
    timerSets.forEach((set) => {
      expect(
        set.querySelector('[data-testid="timer-icon"]')
      ).toBeInTheDocument()
    })
  })

  it('should show check icon for finished sets', () => {
    mockSet1.IsFinished = true

    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    const finishedSet = screen.getByTestId('set-0')
    expect(
      finishedSet.querySelector('[data-testid="check-icon"]')
    ).toBeInTheDocument()
  })

  it('should handle bodyweight exercises', () => {
    mockExercise.IsBodyweight = true
    mockSet2.IsBodyweight = true
    mockSet2.Weight = { Kg: 0, Lb: 0 }

    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    const bodyweightSets = screen.getAllByText('Bodyweight × 8')
    expect(bodyweightSets).toHaveLength(2) // Set 1 and Set 2
  })

  it('should show set type badges', () => {
    mockSet2.IsMaxChallenge = true
    mockSet3.IsDropSet = true

    render(
      <SetListMobile exercise={mockExercise} onSetTap={vi.fn()} massUnit="kg" />
    )

    expect(screen.getByText('MAX')).toBeInTheDocument()
    expect(screen.getByText('DROP')).toBeInTheDocument()
  })
})
