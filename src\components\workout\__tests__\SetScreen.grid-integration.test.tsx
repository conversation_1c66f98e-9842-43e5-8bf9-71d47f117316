import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetScreen } from '../SetScreen'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useSetListMobile } from '@/hooks/useSetListMobile'
import type { RecommendationModel, ExerciseModel } from '@/types'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/workout/exercise/123',
}))

// Mock the hooks
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useSetListMobile')

const mockExercise: ExerciseModel = {
  Id: 123,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsTimeBased: false,
}

const mockRecommendation: RecommendationModel = {
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.2 },
  Series: 3,
  WarmupsCount: 0,
}

const mockSetScreenLogic = {
  currentExercise: mockExercise,
  exercises: [mockExercise],
  currentExerciseIndex: 0,
  isWarmup: false,
  totalSets: 3,
  currentSetIndex: 0,
  setData: { reps: 10, weight: 135, duration: 0 },
  isSaving: false,
  saveError: null,
  showRIRPicker: false,
  showComplete: false,
  showExerciseComplete: false,
  isTransitioning: false,
  showSetSaved: false,
  recommendation: mockRecommendation,
  isLoading: false,
  error: null,
  isLastExercise: false,
  completedSets: [],
  setSetData: vi.fn(),
  handleSaveSet: vi.fn(),
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
  refetchRecommendation: vi.fn(),
  performancePercentage: vi.fn(() => 100),
  handleSetClick: vi.fn(),
}

describe('SetScreen with ExerciseSetsGrid', () => {
  beforeEach(() => {
    vi.mocked(useSetScreenLogic).mockReturnValue(mockSetScreenLogic)
    vi.mocked(useSetListMobile).mockReturnValue({
      exerciseWorkSets: null,
      massUnit: 'lbs',
    })
  })

  describe('Grid Mode Integration', () => {
    it('should render ExerciseSetsGrid when enableGridView is true', () => {
      render(
        <NavigationProvider>
          <SetScreen exerciseId={123} />
        </NavigationProvider>
      )

      // Initially should not show grid headers
      expect(screen.queryByText('SET')).not.toBeInTheDocument()
      expect(screen.queryByText('REPS')).not.toBeInTheDocument()
      expect(screen.queryByText('LBS')).not.toBeInTheDocument()

      // Now test with grid mode enabled (we'll add this prop)
      // For now, let's test that the structure exists for integration
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })

    it('should display all sets in grid format', async () => {
      // Update mock to include completed sets
      const updatedLogic = {
        ...mockSetScreenLogic,
        completedSets: [
          {
            Id: 1,
            SetNo: '1',
            Reps: 10,
            Weight: { Lb: 135, Kg: 61.2 },
            IsFinished: true,
            IsNext: false,
            IsWarmups: false,
          },
          {
            Id: 2,
            SetNo: '2',
            Reps: 8,
            Weight: { Lb: 135, Kg: 61.2 },
            IsFinished: false,
            IsNext: true,
            IsWarmups: false,
          },
        ],
      }

      vi.mocked(useSetScreenLogic).mockReturnValue(updatedLogic)

      render(
        <NavigationProvider>
          <SetScreen exerciseId={123} />
        </NavigationProvider>
      )

      // Should show exercise name
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })

    it('should handle set updates through grid', async () => {
      const handleSetUpdate = vi.fn()
      const updatedLogic = {
        ...mockSetScreenLogic,
        handleSetUpdate,
        completedSets: [
          {
            Id: 1,
            SetNo: '1',
            Reps: 10,
            Weight: { Lb: 135, Kg: 61.2 },
            IsFinished: false,
            IsNext: true,
            IsWarmups: false,
          },
        ],
      }

      vi.mocked(useSetScreenLogic).mockReturnValue(updatedLogic)

      render(
        <NavigationProvider>
          <SetScreen exerciseId={123} />
        </NavigationProvider>
      )

      // Verify current implementation shows set inputs
      const repsInputs = screen.getAllByDisplayValue('10')
      expect(repsInputs.length).toBeGreaterThan(0)
    })

    it('should handle exercise completion through grid', async () => {
      const handleFinishExercise = vi.fn()
      const updatedLogic = {
        ...mockSetScreenLogic,
        handleFinishExercise,
        completedSets: Array(3)
          .fill(null)
          .map((_, i) => ({
            Id: i + 1,
            SetNo: `${i + 1}`,
            Reps: 10 - i,
            Weight: { Lb: 135, Kg: 61.2 },
            IsFinished: true,
            IsNext: false,
            IsWarmups: false,
          })),
      }

      vi.mocked(useSetScreenLogic).mockReturnValue(updatedLogic)

      render(
        <NavigationProvider>
          <SetScreen exerciseId={123} />
        </NavigationProvider>
      )

      // Test current save button functionality
      const saveButton = screen.getByText('Save Set')
      expect(saveButton).toBeInTheDocument()
    })

    it('should maintain compatibility with existing features', () => {
      render(
        <NavigationProvider>
          <SetScreen exerciseId={123} />
        </NavigationProvider>
      )

      // Should still show existing UI elements
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('Save Set')).toBeInTheDocument()

      // Should have input fields (from SetInputs component)
      const inputs = screen.getAllByRole('spinbutton')
      expect(inputs.length).toBeGreaterThan(0)
    })
  })
})
