import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import type { ExerciseModel, RecommendationModel } from '@/types'

/**
 * Exercise work sets collection model
 * Represents a complete exercise with all its sets
 * Matches the MAUI ExerciseWorkSetsModel functionality
 */
export class ExerciseWorkSetsModel {
  // Exercise identification
  public Id: number

  public Label: string

  public CountNo: string

  // Exercise characteristics
  public IsBodyweight: boolean

  public IsSystemExercise: boolean

  public IsFlexibility: boolean

  public IsTimeBased: boolean

  public IsUnilateral: boolean

  // Exercise state
  public IsFinished: boolean

  public IsNextExercise: boolean

  public IsSelected: boolean

  public IsRecoLoaded: boolean

  // Media and display
  public VideoUrl: string

  public HeaderImage: string

  public BodyPartId?: number

  // Sets collection
  private _sets: WorkoutLogSerieModelRef[] = []

  // UI state flags
  public IsAddExercise?: boolean

  public IsFinishWorkoutExe?: boolean

  // Recommendation model
  public RecoModel?: RecommendationModel

  constructor(exercise: ExerciseModel) {
    this.Id = exercise.Id
    this.Label = exercise.Label
    this.CountNo = exercise.Id.toString()
    this.IsBodyweight = exercise.IsBodyweight
    this.IsSystemExercise = exercise.IsSystemExercise
    this.IsFlexibility = exercise.IsFlexibility
    this.IsTimeBased = exercise.IsTimeBased
    this.IsUnilateral = exercise.IsUnilateral
    this.IsFinished = exercise.IsFinished
    this.IsNextExercise = exercise.IsNextExercise
    this.IsSelected = false
    this.IsRecoLoaded = false
    this.VideoUrl = exercise.VideoUrl
    this.HeaderImage = ''
    this.BodyPartId = exercise.BodyPartId
  }

  /**
   * Get all sets
   */
  public get Sets(): WorkoutLogSerieModelRef[] {
    return [...this._sets]
  }

  /**
   * Get count of sets
   */
  public get Count(): number {
    return this._sets.length
  }

  /**
   * Add a set to the collection
   */
  public Add(set: WorkoutLogSerieModelRef): void {
    this._sets.push(set)
  }

  /**
   * Clear all sets
   */
  public Clear(): void {
    this._sets = []
  }

  /**
   * Remove a set at specific index
   */
  public RemoveAt(index: number): void {
    if (index >= 0 && index < this._sets.length) {
      this._sets.splice(index, 1)
    }
  }

  /**
   * Get set at specific index
   */
  public GetAt(index: number): WorkoutLogSerieModelRef | undefined {
    return this._sets[index]
  }

  /**
   * Find set by ID
   */
  public FindById(id: number): WorkoutLogSerieModelRef | undefined {
    return this._sets.find((set) => set.Id === id)
  }

  /**
   * Get warmup sets
   */
  public get WarmupSets(): WorkoutLogSerieModelRef[] {
    return this._sets.filter((set) => set.IsWarmups)
  }

  /**
   * Get work sets (non-warmup)
   */
  public get WorkSets(): WorkoutLogSerieModelRef[] {
    return this._sets.filter((set) => !set.IsWarmups)
  }

  /**
   * Get finished sets
   */
  public get FinishedSets(): WorkoutLogSerieModelRef[] {
    return this._sets.filter((set) => set.IsFinished)
  }

  /**
   * Get unfinished sets
   */
  public get UnfinishedSets(): WorkoutLogSerieModelRef[] {
    return this._sets.filter((set) => !set.IsFinished)
  }

  /**
   * Get next set to perform
   */
  public get NextSet(): WorkoutLogSerieModelRef | undefined {
    return this._sets.find((set) => set.IsNext)
  }

  /**
   * Get current set index
   */
  public get CurrentSetIndex(): number {
    const nextSet = this.NextSet
    if (!nextSet) return -1
    return this._sets.indexOf(nextSet)
  }

  /**
   * Check if all sets are finished
   */
  public get AllSetsFinished(): boolean {
    return this._sets.length > 0 && this._sets.every((set) => set.IsFinished)
  }

  /**
   * Check if any sets are finished
   */
  public get AnySetsFinished(): boolean {
    return this._sets.some((set) => set.IsFinished)
  }

  /**
   * Update IsNext property for sets
   */
  public UpdateNextSet(): void {
    // Clear all IsNext flags
    this._sets.forEach((set) => {
      set.IsNext = false
    })

    // Find first unfinished set and mark as next
    const firstUnfinished = this._sets.find((set) => !set.IsFinished)
    if (firstUnfinished) {
      firstUnfinished.IsNext = true
    }
  }

  /**
   * Mark a set as finished
   */
  public FinishSet(setIndex: number): void {
    if (setIndex >= 0 && setIndex < this._sets.length) {
      const set = this._sets[setIndex]
      if (set) {
        set.IsFinished = true
        this.UpdateNextSet()
      }
    }
  }

  /**
   * Mark a set as unfinished
   */
  public UnfinishSet(setIndex: number): void {
    if (setIndex >= 0 && setIndex < this._sets.length) {
      const set = this._sets[setIndex]
      if (set) {
        set.IsFinished = false
        this.UpdateNextSet()
      }
    }
  }

  /**
   * Update set data
   */
  public UpdateSet(
    setIndex: number,
    updates: Partial<WorkoutLogSerieModelRef>
  ): void {
    if (setIndex >= 0 && setIndex < this._sets.length) {
      const set = this._sets[setIndex]
      if (set) {
        Object.assign(set, updates)
      }
    }
  }

  /**
   * Get exercise completion percentage
   */
  public get CompletionPercentage(): number {
    if (this._sets.length === 0) return 0
    const finishedCount = this.FinishedSets.length
    return Math.round((finishedCount / this._sets.length) * 100)
  }

  /**
   * Check if exercise is ready to finish
   */
  public get CanFinishExercise(): boolean {
    const workSets = this.WorkSets
    return workSets.length > 0 && workSets.every((set) => set.IsFinished)
  }

  /**
   * Finish the exercise
   */
  public FinishExercise(): void {
    this.IsFinished = true
    this._sets.forEach((set) => {
      set.IsExerciseFinished = true
      set.IsNext = false
    })
  }

  /**
   * Reset exercise state
   */
  public Reset(): void {
    this.IsFinished = false
    this._sets.forEach((set) => {
      set.IsFinished = false
      set.IsNext = false
      set.IsExerciseFinished = false
    })
    this.UpdateNextSet()
  }

  /**
   * Convert to JSON for serialization
   */
  public toJSON(): any {
    return {
      Id: this.Id,
      Label: this.Label,
      CountNo: this.CountNo,
      IsBodyweight: this.IsBodyweight,
      IsSystemExercise: this.IsSystemExercise,
      IsFlexibility: this.IsFlexibility,
      IsTimeBased: this.IsTimeBased,
      IsUnilateral: this.IsUnilateral,
      IsFinished: this.IsFinished,
      IsNextExercise: this.IsNextExercise,
      IsSelected: this.IsSelected,
      IsRecoLoaded: this.IsRecoLoaded,
      VideoUrl: this.VideoUrl,
      HeaderImage: this.HeaderImage,
      BodyPartId: this.BodyPartId,
      Sets: this._sets,
      RecoModel: this.RecoModel,
    }
  }

  /**
   * Create from JSON
   */
  public static fromJSON(data: any): ExerciseWorkSetsModel {
    const exercise = {
      Id: data.Id,
      Label: data.Label,
      IsBodyweight: data.IsBodyweight,
      IsSystemExercise: data.IsSystemExercise,
      IsFlexibility: data.IsFlexibility,
      IsTimeBased: data.IsTimeBased,
      IsUnilateral: data.IsUnilateral,
      IsFinished: data.IsFinished,
      IsNextExercise: data.IsNextExercise,
      VideoUrl: data.VideoUrl,
      BodyPartId: data.BodyPartId,
    } as ExerciseModel

    const model = new ExerciseWorkSetsModel(exercise)
    model.IsSelected = data.IsSelected
    model.IsRecoLoaded = data.IsRecoLoaded
    model.HeaderImage = data.HeaderImage
    model.RecoModel = data.RecoModel

    if (data.Sets) {
      data.Sets.forEach((set: WorkoutLogSerieModelRef) => model.Add(set))
    }

    return model
  }
}
