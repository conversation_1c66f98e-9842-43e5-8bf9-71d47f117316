import { useCallback } from 'react'
import { updateOneRM } from '@/utils/oneRmCalculator'
import { replaceWithDot, truncateDecimal } from '@/utils/weightHelpers'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import type { ExerciseModel, RecommendationModel } from '@/types'

interface UseSetInputHandlersMAUIProps {
  exercise: ExerciseModel | null
  recommendation: RecommendationModel | null
  isKg: boolean
  userBodyWeight: number
  onWeightChange?: (weight: number) => void
  onRepsChange?: (reps: number) => void
  onOneRMUpdate?: (data: {
    currentOneRM: number
    progress: number
    progressText: string
    lastTimeText: string
  }) => void
}

/**
 * Set input handlers that match MAUI WeightEntry_TextChanged and RepsEntry_TextChanged
 */
export function useSetInputHandlersMAUI({
  exercise,
  recommendation,
  isKg,
  userBodyWeight,
  onWeightChange,
  onRepsChange,
  onOneRMUpdate,
}: UseSetInputHandlersMAUIProps) {
  /**
   * Handle weight change - matches MAUI WeightEntry_TextChanged
   */
  const handleWeightChange = useCallback(
    (
      value: string,
      setData: WorkoutLogSerieModelRef,
      isFocused: boolean = true
    ) => {
      try {
        if (!isFocused) return

        const txt = value || ''
        if (txt === '') return

        // Replace comma with dot and remove spaces
        const entryText = replaceWithDot(txt.replace(' ', ''))
        const currentWeight = parseFloat(entryText)

        if (Number.isNaN(currentWeight)) return

        if (setData && !entryText.endsWith('.')) {
          // Update weight
          onWeightChange?.(currentWeight)

          // Send message to update 1RM if this is first work set
          if (
            !setData.IsBackOffSet &&
            !setData.IsWarmups &&
            !setData.IsFinished &&
            !setData.IsEditing
          ) {
            const oneRMData = updateOneRM(
              currentWeight,
              setData.Reps || 0,
              exercise,
              recommendation,
              isKg,
              userBodyWeight
            )

            onOneRMUpdate?.(oneRMData)
          }
        }
      } catch (error) {
        console.error('Error in handleWeightChange:', error)
      }
    },
    [
      exercise,
      recommendation,
      isKg,
      userBodyWeight,
      onWeightChange,
      onOneRMUpdate,
    ]
  )

  /**
   * Handle reps change - matches MAUI RepsEntry_TextChanged
   */
  const handleRepsChange = useCallback(
    (
      value: string,
      setData: WorkoutLogSerieModelRef,
      isFocused: boolean = true
    ) => {
      try {
        if (!isFocused) return

        const txt = value || ''
        if (txt.endsWith(',') || txt.endsWith('.') || txt === '') return

        const currentReps = parseInt(txt.replace(',', '').replace('.', ''), 10)

        if (Number.isNaN(currentReps)) return

        if (setData) {
          // Update reps
          onRepsChange?.(currentReps)

          // Send message to update 1RM if this is first work set
          if (
            !setData.IsBackOffSet &&
            !setData.IsWarmups &&
            !setData.IsFinished &&
            !setData.IsEditing
          ) {
            const oneRMData = updateOneRM(
              setData.Weight?.Kg || 0,
              currentReps,
              exercise,
              recommendation,
              isKg,
              userBodyWeight
            )

            onOneRMUpdate?.(oneRMData)
          }
        }
      } catch (error) {
        console.error('Error in handleRepsChange:', error)
      }
    },
    [
      exercise,
      recommendation,
      isKg,
      userBodyWeight,
      onRepsChange,
      onOneRMUpdate,
    ]
  )

  /**
   * Validate weight input
   */
  const validateWeight = useCallback((weight: number): string | null => {
    if (weight < 0) return 'Weight cannot be negative'
    if (weight > 1000) return 'Weight too high'
    return null
  }, [])

  /**
   * Validate reps input
   */
  const validateReps = useCallback((reps: number): string | null => {
    if (reps < 1) return 'Reps must be at least 1'
    if (reps > 100) return 'Reps too high'
    return null
  }, [])

  /**
   * Handle weight increment
   */
  const incrementWeight = useCallback(
    (currentWeight: number): number => {
      const increment = isKg ? 2.5 : 5
      const newWeight = currentWeight + increment
      return truncateDecimal(newWeight, 2)
    },
    [isKg]
  )

  /**
   * Handle weight decrement
   */
  const decrementWeight = useCallback(
    (currentWeight: number): number => {
      const increment = isKg ? 2.5 : 5
      const newWeight = Math.max(0, currentWeight - increment)
      return truncateDecimal(newWeight, 2)
    },
    [isKg]
  )

  /**
   * Handle reps increment
   */
  const incrementReps = useCallback((currentReps: number): number => {
    return Math.min(currentReps + 1, 100)
  }, [])

  /**
   * Handle reps decrement
   */
  const decrementReps = useCallback((currentReps: number): number => {
    return Math.max(currentReps - 1, 1)
  }, [])

  return {
    handleWeightChange,
    handleRepsChange,
    validateWeight,
    validateReps,
    incrementWeight,
    decrementWeight,
    incrementReps,
    decrementReps,
  }
}
