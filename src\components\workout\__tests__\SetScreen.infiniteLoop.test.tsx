import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import * as useSetScreenLogicModule from '@/hooks/useSetScreenLogic'
import * as useSetListMobileModule from '@/hooks/useSetListMobile'
import * as useAuthStoreModule from '@/stores/authStore'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  usePathname: () => '/test',
}))

describe('SetScreen infinite loop bug', () => {
  const mockSetSetData = vi.fn()

  const defaultMockLogic = {
    currentExercise: {
      ExerciseId: 1,
      Label: 'Bench Press',
      IsBodyweight: false,
      IsTimeBased: false,
    },
    exercises: [],
    currentExerciseIndex: 0,
    isWarmup: false,
    totalSets: 3,
    currentSetIndex: 0,
    setData: { weight: 100, reps: 10, duration: 0 },
    isSaving: false,
    saveError: null,
    showRIRPicker: false,
    showComplete: false,
    showExerciseComplete: false,
    isTransitioning: false,
    showSetSaved: false,
    recommendation: {
      FirstWorkSet1RM: { Kg: 120, Lb: 264 },
      FirstWorkSetReps: 8,
      FirstWorkSetWeight: { Kg: 100, Lb: 220 },
    },
    isLoading: false,
    error: null,
    isLastExercise: false,
    setSetData: mockSetSetData,
    handleSaveSet: vi.fn(),
    handleRIRSelect: vi.fn(),
    handleRIRCancel: vi.fn(),
    refetchRecommendation: vi.fn(),
    performancePercentage: () => 100,
    handleSetClick: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useSetScreenLogic
    vi.spyOn(useSetScreenLogicModule, 'useSetScreenLogic').mockReturnValue(
      defaultMockLogic
    )

    // Mock useSetListMobile
    vi.spyOn(useSetListMobileModule, 'useSetListMobile').mockReturnValue({
      exerciseWorkSets: null,
      massUnit: 'lbs',
    })

    // Mock useAuthStore
    vi.spyOn(useAuthStoreModule, 'useAuthStore').mockReturnValue({
      getCachedUserInfo: () => ({ BodyWeight: { Kg: 80, Lb: 176 } }),
    } as any)
  })

  it('should not cause infinite loop when onChange is called with both weight and reps', async () => {
    const queryClient = new QueryClient({
      defaultOptions: { queries: { retry: false } },
    })

    let onChangeCallCount = 0
    let renderCount = 0

    // Track how many times onChange is called
    vi.doMock('../SetInputs-refactored', () => ({
      SetInputs: ({ onChange, weight }: any) => {
        renderCount++

        // Simulate what happens in useSetInputHandlers when reps change
        const simulateRepsChange = () => {
          onChangeCallCount++
          if (onChangeCallCount > 10) {
            throw new Error(
              'Infinite loop detected - onChange called more than 10 times'
            )
          }
          // This is what useSetInputHandlers does - it passes both reps and weight
          onChange({ reps: 12, weight })
        }

        return (
          <div>
            <button onClick={simulateRepsChange}>Change Reps</button>
            <div>Render count: {renderCount}</div>
          </div>
        )
      },
    }))

    const { SetScreen: MockedSetScreen } = await import('../SetScreen')

    render(
      <QueryClientProvider client={queryClient}>
        <NavigationProvider>
          <MockedSetScreen exerciseId={1} />
        </NavigationProvider>
      </QueryClientProvider>
    )

    const changeRepsButton = screen.getByText('Change Reps')

    // Click the button to trigger onChange
    await userEvent.click(changeRepsButton)

    // Wait a bit to see if infinite loop occurs
    await waitFor(
      () => {
        // The component should not re-render excessively
        expect(renderCount).toBeLessThan(5)
      },
      { timeout: 1000 }
    )

    // onChange should only be called once per user interaction
    expect(onChangeCallCount).toBe(1)
  })

  it('identifies the issue where onChange includes unchanged values', () => {
    // This test demonstrates the actual bug scenario
    const setData = { weight: 100, reps: 10, duration: 0 }
    const setSetData = vi.fn()

    // Mock the 1RM handlers that internally call setSetData
    const handleWeightChange = vi.fn((value: string) => {
      setSetData((prev: any) => ({ ...prev, weight: parseFloat(value) }))
    })
    const handleRepsChange = vi.fn((value: string) => {
      setSetData((prev: any) => ({ ...prev, reps: parseInt(value, 10) }))
    })

    // This is the current onChange implementation from SetScreen.tsx
    const currentOnChange = (data: {
      weight?: number
      reps?: number
      duration?: number
    }) => {
      let handled = false

      if (data.weight !== undefined && data.weight !== setData.weight) {
        handleWeightChange(data.weight.toString())
        handled = true
      }

      if (data.reps !== undefined && data.reps !== setData.reps) {
        handleRepsChange(data.reps.toString())
        handled = true
      }

      // For other properties like duration, use setSetData
      if (!handled || data.duration !== undefined) {
        setSetData((prev: any) => ({ ...prev, ...data }))
      }
    }

    // BUG SCENARIO: When useSetInputHandlers calls onChange for reps change,
    // it includes the current weight value too: { reps: 12, weight: 100 }
    currentOnChange({ reps: 12, weight: 100 }) // weight hasn't changed!

    // Only reps handler should be called
    expect(handleRepsChange).toHaveBeenCalledWith('12')
    expect(handleWeightChange).not.toHaveBeenCalled()

    // With the bug, setSetData would be called twice - once by handleRepsChange, once on line 278
    // But this test simulates the current (buggy) behavior for documentation
    expect(setSetData).toHaveBeenCalledTimes(1) // Only called by handleRepsChange
  })

  it('demonstrates the fixed onChange implementation', () => {
    const setData = { weight: 100, reps: 10, duration: 0 }
    const setSetData = vi.fn()

    const handleWeightChange = vi.fn((value: string) => {
      setSetData((prev: any) => ({ ...prev, weight: parseFloat(value) }))
    })
    const handleRepsChange = vi.fn((value: string) => {
      setSetData((prev: any) => ({ ...prev, reps: parseInt(value, 10) }))
    })

    // Fixed implementation
    const fixedOnChange = (data: {
      weight?: number
      reps?: number
      duration?: number
    }) => {
      if (data.weight !== undefined && data.weight !== setData.weight) {
        handleWeightChange(data.weight.toString())
      }

      if (data.reps !== undefined && data.reps !== setData.reps) {
        handleRepsChange(data.reps.toString())
      }

      // Only handle duration separately
      if (data.duration !== undefined) {
        setSetData((prev: any) => ({ ...prev, duration: data.duration }))
      }
    }

    // Same scenario: reps change with current weight included
    fixedOnChange({ reps: 12, weight: 100 })

    // Only reps handler called
    expect(handleRepsChange).toHaveBeenCalledWith('12')
    expect(handleWeightChange).not.toHaveBeenCalled()

    // setSetData called only ONCE by handleRepsChange
    expect(setSetData).toHaveBeenCalledTimes(1)
  })
})
